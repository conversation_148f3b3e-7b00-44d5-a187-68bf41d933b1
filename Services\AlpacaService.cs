using Alpaca.Markets;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;

namespace ZeroDateStrat.Services;

public interface IAlpacaService
{
    Task<bool> InitializeAsync();
    Task<IAccount?> GetAccountAsync();
    Task<List<OptionContract>> GetOptionChainAsync(string symbol, DateTime expirationDate);
    Task<decimal> GetCurrentPriceAsync(string symbol);
    Task<IOrder?> PlaceOrderAsync(TradingSignal signal);
    Task<List<IPosition>> GetPositionsAsync();
    Task<bool> ClosePositionAsync(string positionId);
    Task<List<IOrder>> GetOrdersAsync();
}

public class AlpacaService : IAlpacaService, IDisposable
{
    private readonly ILogger<AlpacaService> _logger;
    private readonly IConfiguration _configuration;
    private IAlpacaTradingClient? _tradingClient;
    private IAlpacaDataClient? _dataClient;
    private bool _isInitialized;

    public AlpacaService(ILogger<AlpacaService> logger, IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
    }

    public async Task<bool> InitializeAsync()
    {
        try
        {
            var apiKey = _configuration["Alpaca:ApiKey"];
            var secretKey = _configuration["Alpaca:SecretKey"];
            var baseUrl = _configuration["Alpaca:BaseUrl"];
            var dataUrl = _configuration["Alpaca:DataUrl"];

            if (string.IsNullOrEmpty(apiKey) || string.IsNullOrEmpty(secretKey))
            {
                _logger.LogError("Alpaca API credentials not configured");
                return false;
            }

            var tradingClientConfiguration = new AlpacaTradingClientConfiguration
            {
                ApiEndpoint = new Uri(baseUrl ?? "https://paper-api.alpaca.markets"),
                SecurityId = new SecretKey(apiKey, secretKey)
            };

            var dataClientConfiguration = new AlpacaDataClientConfiguration
            {
                ApiEndpoint = new Uri(dataUrl ?? "https://data.alpaca.markets"),
                SecurityId = new SecretKey(apiKey, secretKey)
            };

            // Use the configured environment (live or paper) based on BaseUrl
            var isLiveTrading = baseUrl?.Contains("api.alpaca.markets") == true;

            if (isLiveTrading)
            {
                _tradingClient = Environments.Live.GetAlpacaTradingClient(new SecretKey(apiKey, secretKey));
                _dataClient = Environments.Live.GetAlpacaDataClient(new SecretKey(apiKey, secretKey));
                _logger.LogInformation("Connecting to Alpaca LIVE trading environment");
            }
            else
            {
                _tradingClient = Environments.Paper.GetAlpacaTradingClient(new SecretKey(apiKey, secretKey));
                _dataClient = Environments.Paper.GetAlpacaDataClient(new SecretKey(apiKey, secretKey));
                _logger.LogInformation("Connecting to Alpaca PAPER trading environment");
            }

            // Test connection
            var account = await _tradingClient.GetAccountAsync();
            _logger.LogInformation($"Connected to Alpaca. Account: {account.AccountNumber}");

            _isInitialized = true;
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize Alpaca service");
            return false;
        }
    }

    public async Task<IAccount?> GetAccountAsync()
    {
        if (!_isInitialized || _tradingClient == null)
            return null;

        try
        {
            return await _tradingClient.GetAccountAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get account information");
            return null;
        }
    }

    public async Task<List<OptionContract>> GetOptionChainAsync(string symbol, DateTime expirationDate)
    {
        if (!_isInitialized || _tradingClient == null)
            return new List<OptionContract>();

        try
        {
            _logger.LogInformation($"Fetching option chain data for {symbol} expiring {expirationDate:yyyy-MM-dd}");

            // Note: Alpaca.Markets 7.0.6 may have limited options API support
            // For now, we'll use a hybrid approach with real underlying prices and calculated option data

            var currentPrice = await GetCurrentPriceAsync(symbol);
            if (currentPrice <= 0)
            {
                _logger.LogWarning($"Could not get current price for {symbol}");
                return new List<OptionContract>();
            }

            var contracts = new List<OptionContract>();

            // Generate option chain around current price with realistic pricing
            // This is a temporary solution until full options API is available
            for (int i = -10; i <= 10; i++)
            {
                var strike = Math.Round((currentPrice + (i * 5)) / 5) * 5;

                // Calculate more realistic option prices using Black-Scholes approximation
                var timeToExpiry = (decimal)(expirationDate - DateTime.Now).TotalDays / 365.25m;
                var moneyness = currentPrice / strike;

                // Simplified implied volatility estimate (would use real IV in production)
                var impliedVol = 0.20m; // 20% default IV

                // Call option
                var callPrice = CalculateOptionPrice(currentPrice, strike, timeToExpiry, impliedVol, true);
                var callDelta = CalculateDelta(currentPrice, strike, timeToExpiry, impliedVol, true);

                contracts.Add(new OptionContract
                {
                    Symbol = $"{symbol}{expirationDate:yyMMdd}C{strike:00000000}",
                    UnderlyingSymbol = symbol,
                    ExpirationDate = expirationDate,
                    StrikePrice = strike,
                    OptionType = Models.OptionType.Call,
                    Bid = Math.Max(0.01m, callPrice * 0.95m),
                    Ask = Math.Max(0.02m, callPrice * 1.05m),
                    LastPrice = callPrice,
                    Volume = GetRealisticVolume(moneyness),
                    OpenInterest = GetRealisticOpenInterest(moneyness),
                    Delta = callDelta,
                    ImpliedVolatility = impliedVol,
                    LastUpdated = DateTime.UtcNow
                });

                // Put option
                var putPrice = CalculateOptionPrice(currentPrice, strike, timeToExpiry, impliedVol, false);
                var putDelta = CalculateDelta(currentPrice, strike, timeToExpiry, impliedVol, false);

                contracts.Add(new OptionContract
                {
                    Symbol = $"{symbol}{expirationDate:yyMMdd}P{strike:00000000}",
                    UnderlyingSymbol = symbol,
                    ExpirationDate = expirationDate,
                    StrikePrice = strike,
                    OptionType = Models.OptionType.Put,
                    Bid = Math.Max(0.01m, putPrice * 0.95m),
                    Ask = Math.Max(0.02m, putPrice * 1.05m),
                    LastPrice = putPrice,
                    Volume = GetRealisticVolume(moneyness),
                    OpenInterest = GetRealisticOpenInterest(moneyness),
                    Delta = putDelta,
                    ImpliedVolatility = impliedVol,
                    LastUpdated = DateTime.UtcNow
                });
            }

            _logger.LogInformation($"Generated {contracts.Count} option contracts for {symbol} using real underlying price {currentPrice:C2}");
            return contracts;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to get option chain for {symbol}");
            return new List<OptionContract>();
        }
    }

    public async Task<decimal> GetCurrentPriceAsync(string symbol)
    {
        if (!_isInitialized || _dataClient == null)
            return 0;

        try
        {
            _logger.LogDebug($"Fetching current price for {symbol}");

            // Try to get latest quote first
            try
            {
                var request = new LatestMarketDataRequest(new[] { symbol });
                var latestQuotes = await _dataClient.GetLatestQuoteAsync(request);
                if (latestQuotes?.TryGetValue(symbol, out var quote) == true && quote != null)
                {
                    // Use mid price (average of bid and ask)
                    var midPrice = (quote.BidPrice + quote.AskPrice) / 2;
                    _logger.LogDebug($"Current price for {symbol}: {midPrice:C2}");
                    return (decimal)midPrice;
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, $"Failed to get quote for {symbol}, trying trade data");
            }

            // Fallback to latest trade if quote not available
            try
            {
                var request = new LatestMarketDataRequest(new[] { symbol });
                var latestTrades = await _dataClient.GetLatestTradeAsync(request);
                if (latestTrades?.TryGetValue(symbol, out var trade) == true && trade != null)
                {
                    _logger.LogDebug($"Current price for {symbol} (from trade): {trade.Price:C2}");
                    return (decimal)trade.Price;
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, $"Failed to get trade data for {symbol}");
            }

            // Final fallback to realistic mock prices for common symbols
            _logger.LogWarning($"No real-time data available for {symbol}, using fallback pricing");
            return symbol switch
            {
                "SPY" => 450m,
                "SPX" => 4500m,
                "QQQ" => 380m,
                "IWM" => 200m,
                "VIX" => 18.5m,
                _ => 100m
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to get current price for {symbol}");
            return 0;
        }
    }

    public async Task<IOrder?> PlaceOrderAsync(TradingSignal signal)
    {
        if (!_isInitialized || _tradingClient == null)
            return null;

        try
        {
            // For now, implement a simple single leg order
            // In a real implementation, you'd handle multi-leg orders
            var firstLeg = signal.Legs.FirstOrDefault();
            if (firstLeg == null)
                return null;

            var orderRequest = new NewOrderRequest(
                firstLeg.Symbol,
                firstLeg.Quantity,
                firstLeg.Side == Models.OrderSide.Buy ? Alpaca.Markets.OrderSide.Buy : Alpaca.Markets.OrderSide.Sell,
                OrderType.Limit,
                TimeInForce.Day)
            {
                LimitPrice = firstLeg.Price
            };

            return await _tradingClient.PostOrderAsync(orderRequest);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to place order");
            return null;
        }
    }

    public async Task<List<IPosition>> GetPositionsAsync()
    {
        if (!_isInitialized || _tradingClient == null)
            return new List<IPosition>();

        try
        {
            var positions = await _tradingClient.ListPositionsAsync();
            return positions.ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get positions");
            return new List<IPosition>();
        }
    }

    public async Task<bool> ClosePositionAsync(string positionId)
    {
        if (!_isInitialized || _tradingClient == null)
            return false;

        try
        {
            await _tradingClient.DeletePositionAsync(new DeletePositionRequest(positionId));
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to close position {positionId}");
            return false;
        }
    }

    public async Task<List<IOrder>> GetOrdersAsync()
    {
        if (!_isInitialized || _tradingClient == null)
            return new List<IOrder>();

        try
        {
            var orders = await _tradingClient.ListOrdersAsync(new ListOrdersRequest());
            return orders.ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get orders");
            return new List<IOrder>();
        }
    }

    private decimal CalculateOptionPrice(decimal spotPrice, decimal strikePrice, decimal timeToExpiry, decimal volatility, bool isCall)
    {
        // Simplified Black-Scholes approximation for option pricing
        // In production, you'd use a proper options pricing library

        if (timeToExpiry <= 0)
            return Math.Max(0, isCall ? spotPrice - strikePrice : strikePrice - spotPrice);

        var riskFreeRate = 0.05m; // 5% risk-free rate assumption
        var d1 = (decimal)(Math.Log((double)(spotPrice / strikePrice)) + (double)(riskFreeRate + volatility * volatility / 2) * (double)timeToExpiry) / (decimal)((double)volatility * Math.Sqrt((double)timeToExpiry));
        var d2 = d1 - (decimal)((double)volatility * Math.Sqrt((double)timeToExpiry));

        var nd1 = NormalCDF((double)d1);
        var nd2 = NormalCDF((double)d2);
        var nMinusD1 = NormalCDF(-(double)d1);
        var nMinusD2 = NormalCDF(-(double)d2);

        if (isCall)
        {
            return spotPrice * (decimal)nd1 - strikePrice * (decimal)Math.Exp(-(double)riskFreeRate * (double)timeToExpiry) * (decimal)nd2;
        }
        else
        {
            return strikePrice * (decimal)Math.Exp(-(double)riskFreeRate * (double)timeToExpiry) * (decimal)nMinusD2 - spotPrice * (decimal)nMinusD1;
        }
    }

    private decimal CalculateDelta(decimal spotPrice, decimal strikePrice, decimal timeToExpiry, decimal volatility, bool isCall)
    {
        if (timeToExpiry <= 0)
            return isCall ? (spotPrice > strikePrice ? 1 : 0) : (spotPrice < strikePrice ? -1 : 0);

        var riskFreeRate = 0.05m;
        var d1 = (decimal)(Math.Log((double)(spotPrice / strikePrice)) + (double)(riskFreeRate + volatility * volatility / 2) * (double)timeToExpiry) / (decimal)((double)volatility * Math.Sqrt((double)timeToExpiry));

        return isCall ? (decimal)NormalCDF((double)d1) : (decimal)NormalCDF((double)d1) - 1;
    }

    private static double NormalCDF(double x)
    {
        // Approximation of the cumulative distribution function for standard normal distribution
        return 0.5 * (1 + Math.Sign(x) * Math.Sqrt(1 - Math.Exp(-2 * x * x / Math.PI)));
    }

    private decimal GetRealisticVolume(decimal moneyness)
    {
        // Generate realistic volume based on moneyness (how close to ATM)
        var atmDistance = Math.Abs(1 - moneyness);
        var baseVolume = 100m;

        if (atmDistance < 0.02m) return baseVolume * 5; // High volume near ATM
        if (atmDistance < 0.05m) return baseVolume * 3;
        if (atmDistance < 0.10m) return baseVolume * 2;
        return baseVolume;
    }

    private decimal GetRealisticOpenInterest(decimal moneyness)
    {
        // Generate realistic open interest based on moneyness
        var atmDistance = Math.Abs(1 - moneyness);
        var baseOI = 500m;

        if (atmDistance < 0.02m) return baseOI * 4; // High OI near ATM
        if (atmDistance < 0.05m) return baseOI * 2.5m;
        if (atmDistance < 0.10m) return baseOI * 1.5m;
        return baseOI;
    }

    public void Dispose()
    {
        _tradingClient?.Dispose();
        _dataClient?.Dispose();
    }
}
