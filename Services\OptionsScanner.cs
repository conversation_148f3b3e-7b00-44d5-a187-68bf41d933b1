using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;

namespace ZeroDateStrat.Services;

public interface IOptionsScanner
{
    Task<List<OptionChain>> ScanForZeroDteOptionsAsync(List<string> symbols);
    Task<List<TradingSignal>> FindTradingOpportunitiesAsync(List<OptionChain> optionChains);
    Task<List<TradingSignal>> FindPutCreditSpreadOpportunitiesAsync(OptionChain chain);
    Task<List<TradingSignal>> FindCallCreditSpreadOpportunitiesAsync(OptionChain chain);
    Task<List<TradingSignal>> FindIronButterflyOpportunitiesAsync(OptionChain chain);
}

public class OptionsScanner : IOptionsScanner
{
    private readonly ILogger<OptionsScanner> _logger;
    private readonly IConfiguration _configuration;
    private readonly IAlpacaService _alpacaService;
    private readonly IMarketRegimeAnalyzer _marketRegimeAnalyzer;

    public OptionsScanner(
        ILogger<OptionsScanner> logger,
        IConfiguration configuration,
        IAlpacaService alpacaService,
        IMarketRegimeAnalyzer marketRegimeAnalyzer)
    {
        _logger = logger;
        _configuration = configuration;
        _alpacaService = alpacaService;
        _marketRegimeAnalyzer = marketRegimeAnalyzer;
    }

    public async Task<List<OptionChain>> ScanForZeroDteOptionsAsync(List<string> symbols)
    {
        var optionChains = new List<OptionChain>();
        var today = DateTime.Today;

        foreach (var symbol in symbols)
        {
            try
            {
                _logger.LogInformation($"Scanning 0 DTE options for {symbol}");

                var currentPrice = await _alpacaService.GetCurrentPriceAsync(symbol);
                if (currentPrice <= 0)
                {
                    _logger.LogWarning($"Could not get current price for {symbol}");
                    continue;
                }

                var options = await _alpacaService.GetOptionChainAsync(symbol, today);
                if (!options.Any())
                {
                    _logger.LogInformation($"No 0 DTE options found for {symbol}");
                    continue;
                }

                var optionChain = new OptionChain
                {
                    UnderlyingSymbol = symbol,
                    UnderlyingPrice = currentPrice,
                    ExpirationDate = today,
                    LastUpdated = DateTime.UtcNow
                };

                // Separate calls and puts
                optionChain.Calls = options.Where(o => o.OptionType == OptionType.Call).ToList();
                optionChain.Puts = options.Where(o => o.OptionType == OptionType.Put).ToList();

                // Filter for liquid options
                optionChain.Calls = optionChain.Calls.Where(o => o.IsLiquid && o.IsZeroDte).ToList();
                optionChain.Puts = optionChain.Puts.Where(o => o.IsLiquid && o.IsZeroDte).ToList();

                if (optionChain.Calls.Any() || optionChain.Puts.Any())
                {
                    optionChains.Add(optionChain);
                    _logger.LogInformation($"Found {optionChain.Calls.Count} calls and {optionChain.Puts.Count} puts for {symbol}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error scanning options for {symbol}");
            }
        }

        return optionChains;
    }

    public async Task<List<TradingSignal>> FindTradingOpportunitiesAsync(List<OptionChain> optionChains)
    {
        var signals = new List<TradingSignal>();
        var recommendedStrategies = await _marketRegimeAnalyzer.GetRecommendedStrategiesAsync();

        if (!recommendedStrategies.Any())
        {
            _logger.LogInformation("No strategies recommended for current market regime");
            return signals;
        }

        foreach (var chain in optionChains)
        {
            try
            {
                // Only look for strategies recommended by market regime analyzer
                foreach (var strategy in recommendedStrategies)
                {
                    switch (strategy)
                    {
                        case "PutCreditSpread":
                            var putCreditSignals = await FindPutCreditSpreadOpportunitiesAsync(chain);
                            signals.AddRange(putCreditSignals);
                            break;

                        case "CallCreditSpread":
                            var callCreditSignals = await FindCallCreditSpreadOpportunitiesAsync(chain);
                            signals.AddRange(callCreditSignals);
                            break;

                        case "IronButterfly":
                            var butterflySignals = await FindIronButterflyOpportunitiesAsync(chain);
                            signals.AddRange(butterflySignals);
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error finding opportunities for {chain.UnderlyingSymbol}");
            }
        }

        // Sort by strategy priority, then confidence
        return signals.Where(s => s.IsValid)
                     .OrderBy(s => GetStrategyPriority(s.Strategy))
                     .ThenByDescending(s => s.Confidence)
                     .ThenByDescending(s => s.RiskRewardRatio)
                     .ToList();
    }

    public async Task<List<TradingSignal>> FindPutCreditSpreadOpportunitiesAsync(OptionChain chain)
    {
        var signals = new List<TradingSignal>();

        if (!_configuration.GetValue<bool>("Strategies:PutCreditSpread:Enabled", true))
            return signals;

        try
        {
            var wingWidth = _configuration.GetValue<decimal>("Strategies:PutCreditSpread:WingWidth", 10);
            var targetDelta = _configuration.GetValue<decimal>("Strategies:PutCreditSpread:TargetDelta", 0.10m);
            var minCredit = _configuration.GetValue<decimal>("Strategies:PutCreditSpread:MinCredit", 0.10m);
            var maxCredit = _configuration.GetValue<decimal>("Strategies:PutCreditSpread:MaxCredit", 0.30m);

            // Find puts with target delta (5-15 delta range)
            var suitablePuts = chain.Puts.Where(p =>
                Math.Abs(p.Delta) >= 0.05m &&
                Math.Abs(p.Delta) <= 0.15m &&
                p.IsLiquid &&
                p.StrikePrice < chain.UnderlyingPrice).ToList();

            foreach (var shortPut in suitablePuts)
            {
                var longPutStrike = shortPut.StrikePrice - wingWidth;
                var longPut = chain.GetPutByStrike(longPutStrike);

                if (longPut != null && longPut.IsLiquid)
                {
                    var credit = shortPut.MidPrice - longPut.MidPrice;
                    var maxLoss = wingWidth - credit;

                    if (credit >= minCredit && credit <= maxCredit && maxLoss > 0)
                    {
                        var signal = new TradingSignal
                        {
                            Strategy = "PutCreditSpread",
                            UnderlyingSymbol = chain.UnderlyingSymbol,
                            Type = SignalType.PutSpread,
                            ExpirationDate = chain.ExpirationDate,
                            ExpectedProfit = credit * 0.5m, // Target 50% profit
                            MaxLoss = maxLoss,
                            Confidence = CalculatePutCreditSpreadConfidence(shortPut, longPut, credit, chain),
                            Reason = $"Put Credit Spread: {shortPut.StrikePrice}/{longPut.StrikePrice}, Credit={credit:C2}, Delta={shortPut.Delta:F2}",
                            Legs = new List<OptionLeg>
                            {
                                new() { Symbol = shortPut.Symbol, OptionType = OptionType.Put, StrikePrice = shortPut.StrikePrice, Side = OrderSide.Sell, Quantity = 1, Price = shortPut.MidPrice, Delta = shortPut.Delta },
                                new() { Symbol = longPut.Symbol, OptionType = OptionType.Put, StrikePrice = longPut.StrikePrice, Side = OrderSide.Buy, Quantity = 1, Price = longPut.MidPrice, Delta = longPut.Delta }
                            }
                        };

                        signals.Add(signal);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error finding put credit spread opportunities for {chain.UnderlyingSymbol}");
        }

        return signals.Take(2).ToList(); // Limit to top 2 opportunities
    }

    public async Task<List<TradingSignal>> FindCallCreditSpreadOpportunitiesAsync(OptionChain chain)
    {
        var signals = new List<TradingSignal>();

        if (!_configuration.GetValue<bool>("Strategies:CallCreditSpread:Enabled", true))
            return signals;

        try
        {
            var wingWidth = _configuration.GetValue<decimal>("Strategies:CallCreditSpread:WingWidth", 10);
            var minCredit = _configuration.GetValue<decimal>("Strategies:CallCreditSpread:MinCredit", 0.10m);
            var maxCredit = _configuration.GetValue<decimal>("Strategies:CallCreditSpread:MaxCredit", 0.30m);

            // Find calls with target delta (5-15 delta range)
            var suitableCalls = chain.Calls.Where(c =>
                c.Delta >= 0.05m &&
                c.Delta <= 0.15m &&
                c.IsLiquid &&
                c.StrikePrice > chain.UnderlyingPrice).ToList();

            foreach (var shortCall in suitableCalls)
            {
                var longCallStrike = shortCall.StrikePrice + wingWidth;
                var longCall = chain.GetCallByStrike(longCallStrike);

                if (longCall != null && longCall.IsLiquid)
                {
                    var credit = shortCall.MidPrice - longCall.MidPrice;
                    var maxLoss = wingWidth - credit;

                    if (credit >= minCredit && credit <= maxCredit && maxLoss > 0)
                    {
                        var signal = new TradingSignal
                        {
                            Strategy = "CallCreditSpread",
                            UnderlyingSymbol = chain.UnderlyingSymbol,
                            Type = SignalType.CallSpread,
                            ExpirationDate = chain.ExpirationDate,
                            ExpectedProfit = credit * 0.5m,
                            MaxLoss = maxLoss,
                            Confidence = CalculateCallCreditSpreadConfidence(shortCall, longCall, credit, chain),
                            Reason = $"Call Credit Spread: {shortCall.StrikePrice}/{longCall.StrikePrice}, Credit={credit:C2}, Delta={shortCall.Delta:F2}",
                            Legs = new List<OptionLeg>
                            {
                                new() { Symbol = shortCall.Symbol, OptionType = OptionType.Call, StrikePrice = shortCall.StrikePrice, Side = OrderSide.Sell, Quantity = 1, Price = shortCall.MidPrice, Delta = shortCall.Delta },
                                new() { Symbol = longCall.Symbol, OptionType = OptionType.Call, StrikePrice = longCall.StrikePrice, Side = OrderSide.Buy, Quantity = 1, Price = longCall.MidPrice, Delta = longCall.Delta }
                            }
                        };

                        signals.Add(signal);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error finding call credit spread opportunities for {chain.UnderlyingSymbol}");
        }

        return signals.Take(2).ToList();
    }

    public async Task<List<TradingSignal>> FindIronButterflyOpportunitiesAsync(OptionChain chain)
    {
        var signals = new List<TradingSignal>();

        if (!_configuration.GetValue<bool>("Strategies:IronButterfly:Enabled", true))
            return signals;

        try
        {
            var wingWidth = _configuration.GetValue<decimal>("Strategies:IronButterfly:WingWidth", 25);
            var minCredit = _configuration.GetValue<decimal>("Strategies:IronButterfly:MinCredit", 0.50m);
            var maxCredit = _configuration.GetValue<decimal>("Strategies:IronButterfly:MaxCredit", 1.50m);

            // Find ATM strike (round to nearest $5 for SPX)
            var atmStrike = Math.Round(chain.UnderlyingPrice / 5) * 5;

            // Iron Butterfly: Sell ATM straddle, buy wings
            var atmCall = chain.GetCallByStrike(atmStrike);
            var atmPut = chain.GetPutByStrike(atmStrike);
            var longCall = chain.GetCallByStrike(atmStrike + wingWidth);
            var longPut = chain.GetPutByStrike(atmStrike - wingWidth);

            if (atmCall != null && atmPut != null && longCall != null && longPut != null &&
                atmCall.IsLiquid && atmPut.IsLiquid && longCall.IsLiquid && longPut.IsLiquid)
            {
                var credit = (atmCall.MidPrice + atmPut.MidPrice) - (longCall.MidPrice + longPut.MidPrice);
                var maxLoss = wingWidth - credit;

                if (credit >= minCredit && credit <= maxCredit && maxLoss > 0)
                {
                    var signal = new TradingSignal
                    {
                        Strategy = "IronButterfly",
                        UnderlyingSymbol = chain.UnderlyingSymbol,
                        Type = SignalType.IronButterfly,
                        ExpirationDate = chain.ExpirationDate,
                        ExpectedProfit = credit * 0.5m,
                        MaxLoss = maxLoss,
                        Confidence = CalculateIronButterflyConfidence(atmCall, atmPut, credit, chain),
                        Reason = $"Iron Butterfly: ATM={atmStrike}, Credit={credit:C2}, Wings=±{wingWidth}",
                        Legs = new List<OptionLeg>
                        {
                            new() { Symbol = atmCall.Symbol, OptionType = OptionType.Call, StrikePrice = atmStrike, Side = OrderSide.Sell, Quantity = 1, Price = atmCall.MidPrice },
                            new() { Symbol = atmPut.Symbol, OptionType = OptionType.Put, StrikePrice = atmStrike, Side = OrderSide.Sell, Quantity = 1, Price = atmPut.MidPrice },
                            new() { Symbol = longCall.Symbol, OptionType = OptionType.Call, StrikePrice = atmStrike + wingWidth, Side = OrderSide.Buy, Quantity = 1, Price = longCall.MidPrice },
                            new() { Symbol = longPut.Symbol, OptionType = OptionType.Put, StrikePrice = atmStrike - wingWidth, Side = OrderSide.Buy, Quantity = 1, Price = longPut.MidPrice }
                        }
                    };

                    signals.Add(signal);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error finding iron butterfly opportunities for {chain.UnderlyingSymbol}");
        }

        return signals.Take(1).ToList(); // Only one iron butterfly per underlying
    }

    private int GetStrategyPriority(string strategy)
    {
        return strategy switch
        {
            "PutCreditSpread" => 1,
            "CallCreditSpread" => 2,
            "IronButterfly" => 3,
            "IronCondor" => 4,
            _ => 99
        };
    }

    private decimal CalculatePutCreditSpreadConfidence(OptionContract shortPut, OptionContract longPut, decimal credit, OptionChain chain)
    {
        try
        {
            // Risk/reward ratio (higher is better)
            var maxLoss = Math.Abs(shortPut.StrikePrice - longPut.StrikePrice) - credit;
            var riskReward = maxLoss > 0 ? credit / maxLoss : 0;

            // Liquidity score (higher volume = better)
            var liquidityScore = Math.Min(1.0m, (shortPut.Volume + longPut.Volume) / 50);

            // Delta appropriateness (closer to target delta = better)
            var targetDelta = 0.10m;
            var deltaScore = 1 - Math.Abs(Math.Abs(shortPut.Delta) - targetDelta) / targetDelta;

            // Distance from current price (further OTM = safer)
            var distanceScore = Math.Min(1.0m, (chain.UnderlyingPrice - shortPut.StrikePrice) / chain.UnderlyingPrice * 10);

            // Time decay benefit (always high for 0 DTE)
            var timeDecayScore = 0.9m;

            var confidence = (riskReward * 0.25m) + (liquidityScore * 0.20m) + (deltaScore * 0.25m) +
                           (distanceScore * 0.15m) + (timeDecayScore * 0.15m);

            return Math.Min(0.95m, Math.Max(0.1m, confidence));
        }
        catch
        {
            return 0.5m; // Default confidence
        }
    }

    private decimal CalculateCallCreditSpreadConfidence(OptionContract shortCall, OptionContract longCall, decimal credit, OptionChain chain)
    {
        try
        {
            var maxLoss = Math.Abs(longCall.StrikePrice - shortCall.StrikePrice) - credit;
            var riskReward = maxLoss > 0 ? credit / maxLoss : 0;
            var liquidityScore = Math.Min(1.0m, (shortCall.Volume + longCall.Volume) / 50);
            var targetDelta = 0.10m;
            var deltaScore = 1 - Math.Abs(shortCall.Delta - targetDelta) / targetDelta;
            var distanceScore = Math.Min(1.0m, (shortCall.StrikePrice - chain.UnderlyingPrice) / chain.UnderlyingPrice * 10);
            var timeDecayScore = 0.9m;

            var confidence = (riskReward * 0.25m) + (liquidityScore * 0.20m) + (deltaScore * 0.25m) +
                           (distanceScore * 0.15m) + (timeDecayScore * 0.15m);

            return Math.Min(0.95m, Math.Max(0.1m, confidence));
        }
        catch
        {
            return 0.5m;
        }
    }

    private decimal CalculateIronButterflyConfidence(OptionContract atmCall, OptionContract atmPut, decimal credit, OptionChain chain)
    {
        try
        {
            // Credit quality (higher credit = better, but not too high)
            var creditScore = credit >= 0.50m && credit <= 1.50m ? 0.9m : 0.5m;

            // ATM liquidity (critical for iron butterfly)
            var liquidityScore = Math.Min(1.0m, (atmCall.Volume + atmPut.Volume) / 100);

            // Volatility appropriateness (lower IV = better for selling premium)
            var ivScore = atmCall.ImpliedVolatility > 0 ? Math.Max(0.3m, 1 - (atmCall.ImpliedVolatility / 0.5m)) : 0.7m;

            // Time decay benefit
            var timeDecayScore = 0.95m; // Very high for 0 DTE

            // Market neutrality benefit (works best in range-bound markets)
            var neutralityScore = 0.8m;

            var confidence = (creditScore * 0.25m) + (liquidityScore * 0.25m) + (ivScore * 0.20m) +
                           (timeDecayScore * 0.15m) + (neutralityScore * 0.15m);

            return Math.Min(0.95m, Math.Max(0.1m, confidence));
        }
        catch
        {
            return 0.6m;
        }
    }
}
